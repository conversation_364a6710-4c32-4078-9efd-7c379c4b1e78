'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  LayoutDashboard,
  BookOpen,
  Briefcase,
  Users,
  MessageSquare,
  User,
  Award,
  TrendingUp,
  Calendar,
  FileText,
  Settings,
  Bell,
  LogOut,
  Menu,
  X,
  ChevronDown,
  Rocket,
  GraduationCap,
  Target,
  Clock,
} from 'lucide-react';

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  badge?: number;
  children?: NavItem[];
}

interface StudentNavbarProps {
  coursesEnrolled?: number;
  pendingApplications?: number;
  upcomingSessions?: number;
}

export function StudentNavbar({
  coursesEnrolled = 0,
  pendingApplications = 0,
  upcomingSessions = 0
}: StudentNavbarProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const navigationItems: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard/student',
      icon: LayoutDashboard,
    },
    {
      name: 'Learning',
      href: '/dashboard/student/learning',
      icon: BookOpen,
      badge: coursesEnrolled,
      children: [
        { name: 'My Courses', href: '/courses/enrolled', icon: BookOpen },
        { name: 'Browse Courses', href: '/courses', icon: GraduationCap },
        { name: 'Certificates', href: '/certificates', icon: Award },
        { name: 'Progress', href: '/dashboard/student/progress', icon: TrendingUp },
      ],
    },
    {
      name: 'Career',
      href: '/dashboard/student/career',
      icon: Briefcase,
      badge: pendingApplications,
      children: [
        { name: 'Job Board', href: '/jobs?from=student', icon: Briefcase },
        { name: 'My Applications', href: '/jobs/applications', icon: FileText },
        { name: 'Assessments', href: '/assessments?from=student', icon: Target },
        { name: 'Career Resources', href: '/career/resources', icon: BookOpen },
      ],
    },
    {
      name: 'Mentorship',
      href: '/mentorship?from=student',
      icon: Users,
      badge: upcomingSessions,
      children: [
        { name: 'Find Mentors', href: '/mentorship/find', icon: Users },
        { name: 'My Sessions', href: '/mentorship/sessions', icon: Calendar },
        { name: 'Schedule Session', href: '/mentorship/book', icon: Clock },
      ],
    },
    {
      name: 'Community',
      href: '/community?from=student',
      icon: MessageSquare,
      children: [
        { name: 'Forums', href: '/community/forums', icon: MessageSquare },
        { name: 'Events', href: '/events?from=student', icon: Calendar },
        { name: 'Study Groups', href: '/community/groups', icon: Users },
      ],
    },
    {
      name: 'Profile',
      href: '/dashboard/student/profile',
      icon: User,
      children: [
        { name: 'My Profile', href: '/profile', icon: User },
        { name: 'Achievements', href: '/profile/achievements', icon: Award },
        { name: 'Settings', href: '/settings', icon: Settings },
      ],
    },
  ];

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  const isActive = (href: string) => {
    if (href === '/dashboard/student') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  const isChildActive = (children: NavItem[]) => {
    return children.some(child => isActive(child.href));
  };

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' });
  };

  // Auto-expand active parent items
  useEffect(() => {
    navigationItems.forEach(item => {
      if (item.children && isChildActive(item.children)) {
        setExpandedItems(prev =>
          prev.includes(item.name) ? prev : [...prev, item.name]
        );
      }
    });
  }, [pathname]);

  const NavItemComponent = ({ item, level = 0 }: { item: NavItem; level?: number }) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.name);
    const active = isActive(item.href);
    const childActive = hasChildren && isChildActive(item.children);

    return (
      <div>
        <Link
          href={item.href}
          className={`flex items-center justify-between px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
            active || childActive
              ? 'bg-blue-500/20 text-blue-300 border border-blue-400/30'
              : 'text-gray-300 hover:bg-white/5 hover:text-white'
          } ${level > 0 ? 'ml-4 pl-8' : ''}`}
          onClick={(e) => {
            if (hasChildren) {
              e.preventDefault();
              toggleExpanded(item.name);
            }
          }}
        >
          <div className="flex items-center space-x-3">
            <item.icon className={`h-5 w-5 ${active || childActive ? 'text-blue-400' : 'text-gray-400'}`} />
            <span>{item.name}</span>
            {item.badge && item.badge > 0 && (
              <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-pink-300 bg-pink-500/20 border border-pink-400/30 rounded-full">
                {item.badge}
              </span>
            )}
          </div>
          {hasChildren && (
            <ChevronDown
              className={`h-4 w-4 text-gray-400 transition-transform duration-200 ${
                isExpanded ? 'rotate-180' : ''
              }`}
            />
          )}
        </Link>

        {hasChildren && (
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="overflow-hidden"
              >
                <div className="py-2 space-y-1">
                  {item.children.map((child) => (
                    <NavItemComponent key={child.name} item={child} level={level + 1} />
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        )}
      </div>
    );
  };

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="p-2 rounded-lg muted-glassmorphic shadow-lg hover:bg-white/10"
        >
          {isMobileMenuOpen ? (
            <X className="h-6 w-6 text-white" />
          ) : (
            <Menu className="h-6 w-6 text-white" />
          )}
        </button>
      </div>

      {/* Mobile overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-40 w-64 muted-glassmorphic backdrop-blur-xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
        isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-center h-16 px-4 border-b border-white/10">
            <Link href="/" className="flex items-center space-x-2">
              <div className="relative">
                <Rocket className="h-8 w-8 text-blue-400" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-pink-400 rounded-full animate-pulse" />
              </div>
              <div>
                <span className="text-xl font-bold text-white">Nova</span>
                <span className="block text-xs text-gray-300">Student Portal</span>
              </div>
            </Link>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {navigationItems.map((item) => (
              <NavItemComponent key={item.name} item={item} />
            ))}
          </nav>

          {/* User Profile */}
          <div className="p-4 border-t border-white/10">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-400/30">
                <span className="text-blue-300 font-semibold text-sm">
                  {session?.user?.name?.charAt(0)?.toUpperCase() || 'S'}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {session?.user?.name || 'Student'}
                </p>
                <p className="text-xs text-gray-300 truncate">
                  {session?.user?.email}
                </p>
              </div>
            </div>
            <button
              onClick={handleSignOut}
              className="w-full flex items-center px-3 py-2 text-sm text-gray-300 hover:bg-red-500/20 hover:text-red-300 rounded-lg transition-colors duration-200"
            >
              <LogOut className="h-4 w-4 mr-3" />
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
