'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  LayoutDashboard,
  Briefcase,
  FileText,
  BarChart3,
  Building2,
  Settings,
  Bell,
  LogOut,
  Menu,
  X,
  ChevronDown,
  Rocket,
  Plus,
  Users,
  Calendar,
  Eye,
  TrendingUp,
  BookOpen,
  GraduationCap,
  MessageSquare,
  Shield,
  Award,
  HandHeart,
  FolderOpen,
  Globe,
} from 'lucide-react';

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  badge?: number;
  children?: NavItem[];
}

interface OrganisationNavbarProps {
  newApplications?: number;
  activeJobs?: number;
  activeCourses?: number;
  upcomingEvents?: number;
}

export function OrganisationNavbar({
  newApplications = 0,
  activeJobs = 0,
  activeCourses = 0,
  upcomingEvents = 0
}: OrganisationNavbarProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const navigationItems: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard/organisation',
      icon: LayoutDashboard,
    },
    {
      name: 'Recruitment',
      href: '/dashboard/organisation/jobs',
      icon: Briefcase,
      badge: activeJobs,
      children: [
        { name: 'All Jobs', href: '/dashboard/organisation/jobs', icon: Briefcase },
        { name: 'Post New Job', href: '/jobs/create', icon: Plus },
        { name: 'Job Board', href: '/jobs?from=organisation', icon: Briefcase },
        { name: 'Applications', href: '/dashboard/organisation/applications', icon: FileText },
        { name: 'Interviews', href: '/dashboard/organisation/interviews', icon: Calendar },
        { name: 'Hired Candidates', href: '/dashboard/organisation/hired', icon: Users },
      ],
    },
    {
      name: 'Education',
      href: '/dashboard/organisation/courses',
      icon: BookOpen,
      badge: activeCourses,
      children: [
        { name: 'All Courses', href: '/dashboard/organisation/courses', icon: BookOpen },
        { name: 'Create Course', href: '/courses/create', icon: Plus },
        { name: 'Students', href: '/dashboard/organisation/students', icon: Users },
        { name: 'Certificates', href: '/certificates?from=organisation', icon: GraduationCap },
      ],
    },
    {
      name: 'Events',
      href: '/dashboard/organisation/events',
      icon: Calendar,
      badge: upcomingEvents,
      children: [
        { name: 'All Events', href: '/dashboard/organisation/events', icon: Calendar },
        { name: 'Create Event', href: '/events/create', icon: Plus },
        { name: 'Events Board', href: '/events?from=organisation', icon: Calendar },
        { name: 'Registrations', href: '/dashboard/organisation/event-registrations', icon: Users },
      ],
    },
    {
      name: 'Mentorship',
      href: '/dashboard/organisation/mentorship',
      icon: HandHeart,
      children: [
        { name: 'Sessions', href: '/dashboard/organisation/mentorship', icon: HandHeart },
        { name: 'Create Program', href: '/mentorship/create', icon: Plus },
        { name: 'Mentorship Board', href: '/mentorship?from=organisation', icon: HandHeart },
        { name: 'Mentees', href: '/dashboard/organisation/mentees', icon: Users },
      ],
    },
    {
      name: 'Community',
      href: '/dashboard/organisation/community',
      icon: MessageSquare,
      children: [
        { name: 'Posts', href: '/dashboard/organisation/community', icon: MessageSquare },
        { name: 'Moderate', href: '/community/moderate', icon: Shield },
        { name: 'Community Board', href: '/community?from=organisation', icon: MessageSquare },
        { name: 'Create Post', href: '/community/create', icon: Plus },
      ],
    },
    {
      name: 'Resources',
      href: '/dashboard/organisation/resources',
      icon: FolderOpen,
      children: [
        { name: 'All Resources', href: '/dashboard/organisation/resources', icon: FolderOpen },
        { name: 'Upload Resource', href: '/resources/upload', icon: Plus },
        { name: 'Resources Board', href: '/resources?from=organisation', icon: FolderOpen },
      ],
    },
    {
      name: 'Analytics',
      href: '/dashboard/organisation/analytics',
      icon: BarChart3,
      children: [
        { name: 'Overview', href: '/dashboard/organisation/analytics', icon: BarChart3 },
        { name: 'Recruitment', href: '/dashboard/organisation/analytics/recruitment', icon: Briefcase },
        { name: 'Education', href: '/dashboard/organisation/analytics/education', icon: BookOpen },
        { name: 'Events', href: '/dashboard/organisation/analytics/events', icon: Calendar },
        { name: 'Community', href: '/dashboard/organisation/analytics/community', icon: MessageSquare },
      ],
    },
    {
      name: 'Profile',
      href: '/dashboard/organisation/profile',
      icon: Building2,
    },
    {
      name: 'Settings',
      href: '/dashboard/organisation/settings',
      icon: Settings,
    },
  ];

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  const isActive = (href: string) => {
    if (href === '/dashboard/organisation') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  const isChildActive = (children: NavItem[]) => {
    return children.some(child => isActive(child.href));
  };

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' });
  };

  // Auto-expand active parent items
  useEffect(() => {
    navigationItems.forEach(item => {
      if (item.children && isChildActive(item.children)) {
        setExpandedItems(prev =>
          prev.includes(item.name) ? prev : [...prev, item.name]
        );
      }
    });
  }, [pathname]);

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="p-2 rounded-md bg-white shadow-lg text-gray-600 hover:text-gray-900 hover:bg-gray-50"
        >
          {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </button>
      </div>

      {/* Mobile menu overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 z-30 bg-black bg-opacity-50"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-40 w-64 muted-glassmorphic backdrop-blur-xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
        isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-center h-16 px-4 border-b border-white/10">
            <Link href="/" className="flex items-center space-x-2">
              <div className="relative">
                <Rocket className="h-8 w-8 text-blue-400" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-pink-400 rounded-full animate-pulse" />
              </div>
              <span className="text-xl font-bold text-white">Nova</span>
            </Link>
          </div>

          {/* User info */}
          <div className="p-4 border-b border-white/10">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-400/30">
                <Building2 className="h-5 w-5 text-blue-400" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {session?.user?.name || 'Organization'}
                </p>
                <p className="text-xs text-gray-300 truncate">Organization Dashboard</p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-4 space-y-2 overflow-y-auto">
            {navigationItems.map((item) => (
              <div key={item.name}>
                {item.children ? (
                  <div>
                    <button
                      onClick={() => toggleExpanded(item.name)}
                      className={`w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        isChildActive(item.children)
                          ? 'bg-blue-500/20 text-blue-300 border border-blue-400/30'
                          : 'text-gray-300 hover:bg-white/5 hover:text-white'
                      }`}
                    >
                      <div className="flex items-center">
                        <item.icon className="mr-3 h-5 w-5" />
                        <span>{item.name}</span>
                        {item.badge && item.badge > 0 && (
                          <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-500/20 text-pink-300 border border-pink-400/30">
                            {item.badge}
                          </span>
                        )}
                      </div>
                      <ChevronDown
                        className={`h-4 w-4 transition-transform ${
                          expandedItems.includes(item.name) ? 'rotate-180' : ''
                        }`}
                      />
                    </button>
                    <AnimatePresence>
                      {expandedItems.includes(item.name) && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.2 }}
                          className="overflow-hidden"
                        >
                          <div className="ml-6 mt-1 space-y-1">
                            {item.children.map((child) => (
                              <Link
                                key={child.href}
                                href={child.href}
                                onClick={() => setIsMobileMenuOpen(false)}
                                className={`flex items-center px-3 py-2 text-sm rounded-md transition-colors ${
                                  isActive(child.href)
                                    ? 'bg-purple-500/20 text-purple-300 font-medium border border-purple-400/30'
                                    : 'text-gray-400 hover:bg-white/5 hover:text-white'
                                }`}
                              >
                                <child.icon className="mr-3 h-4 w-4" />
                                <span>{child.name}</span>
                              </Link>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      isActive(item.href)
                        ? 'bg-blue-500/20 text-blue-300 border border-blue-400/30'
                        : 'text-gray-300 hover:bg-white/5 hover:text-white'
                    }`}
                  >
                    <item.icon className="mr-3 h-5 w-5" />
                    <span>{item.name}</span>
                    {item.badge && item.badge > 0 && (
                      <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-500/20 text-pink-300 border border-pink-400/30">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                )}
              </div>
            ))}
          </nav>

          {/* Sign out button */}
          <div className="p-4 border-t border-white/10">
            <button
              onClick={handleSignOut}
              className="w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-red-500/20 hover:text-red-300 transition-colors"
            >
              <LogOut className="mr-3 h-5 w-5" />
              <span>Sign Out</span>
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
