'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Mail, ArrowLeft, ArrowRight, Rocket, CheckCircle } from 'lucide-react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // TODO: Implement password reset logic
    setTimeout(() => {
      setIsLoading(false);
      setIsSubmitted(true);
    }, 2000);
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen galaxy-background">
        {/* Star Field Background */}
        <div className="star-field"></div>

        {/* Muted Galaxy Background Overlay */}
        <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

        <Header />

        <div className="relative pt-20 pb-16 z-10">
          <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="muted-glassmorphic rounded-2xl p-8 text-center glow-border"
            >
              <div className="flex justify-center mb-6">
                <CheckCircle className="h-16 w-16 galaxy-text-pink" />
              </div>

              <h1 className="text-2xl font-bold text-white mb-4 glow-text font-orbitron">Check Your Email</h1>

              <p className="text-gray-300 mb-6">
                We've sent a password reset link to <strong className="galaxy-text-blue">{email}</strong>.
                Please check your email and follow the instructions to reset your password.
              </p>

              <div className="space-y-4">
                <p className="text-sm text-gray-400">
                  Didn't receive the email? Check your spam folder or{' '}
                  <button
                    onClick={() => setIsSubmitted(false)}
                    className="galaxy-text-blue hover:galaxy-text-purple font-medium transition-colors"
                  >
                    try again
                  </button>
                </p>

                <Link
                  href="/auth/signin"
                  className="inline-flex items-center space-x-2 galaxy-text-blue hover:galaxy-text-purple transition-colors"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span>Back to Sign In</span>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>

        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen galaxy-background">
      {/* Star Field Background */}
      <div className="star-field"></div>

      {/* Muted Galaxy Background Overlay */}
      <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

      <Header />

      <div className="relative pt-20 pb-16 z-10">
        <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="muted-glassmorphic rounded-2xl p-8 glow-border"
          >
            {/* Header */}
            <div className="text-center mb-8">
              <div className="flex justify-center mb-4">
                <div className="relative">
                  <Rocket className="h-12 w-12 galaxy-text-blue" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full animate-twinkle" />
                </div>
              </div>
              <h1 className="text-2xl font-bold text-white mb-2 glow-text font-orbitron">Forgot Password?</h1>
              <p className="text-gray-300">
                No worries! Enter your email address and we'll send you a link to reset your password.
              </p>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email Field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                  Email Address
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 galaxy-text-blue" />
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 muted-glassmorphic rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 glow-border"
                    placeholder="Enter your email address"
                    required
                  />
                </div>
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-blue-600 to-pink-600 text-white py-3 px-4 rounded-lg hover:from-pink-600 hover:to-purple-600 transition-all duration-300 flex items-center justify-center space-x-2 group disabled:opacity-50 disabled:cursor-not-allowed glow-border font-semibold"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    <span>Sending...</span>
                  </>
                ) : (
                  <>
                    <span>Send Reset Link</span>
                    <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
                  </>
                )}
              </button>
            </form>

            {/* Back to Sign In */}
            <div className="mt-6 text-center">
              <Link
                href="/auth/signin"
                className="inline-flex items-center space-x-2 galaxy-text-blue hover:galaxy-text-purple transition-colors"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Sign In</span>
              </Link>
            </div>

            {/* Help Text */}
            <div className="mt-8 p-4 muted-glassmorphic rounded-lg glow-border">
              <h3 className="text-sm font-medium text-white mb-2 font-orbitron">Need help?</h3>
              <p className="text-sm text-gray-300">
                If you're still having trouble accessing your account, please{' '}
                <Link href="/contact" className="galaxy-text-blue hover:galaxy-text-purple transition-colors underline hover:no-underline">
                  contact our support team
                </Link>{' '}
                for assistance.
              </p>
            </div>
          </motion.div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
